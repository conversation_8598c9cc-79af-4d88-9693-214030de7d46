#!/usr/bin/env python3
"""
Management script for the job portal application.
Provides commands for RSS updates, job alert processing, and database management.
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from custom_libs.rss_manager import rss_manager
from custom_libs.notification_system import notification_system
from custom_libs.scheduler import job_scheduler


def update_rss():
    """Update RSS feeds manually."""
    print("🔄 Starting RSS feed update...")
    
    with app.app_context():
        stats = rss_manager.update_all_feeds()
        
        print(f"✅ RSS update completed!")
        print(f"📊 Statistics:")
        print(f"   - Jobs cached: {stats.get('total_cached', 0)}")
        print(f"   - Duration: {stats.get('duration', 0):.2f} seconds")
        print(f"   - Errors: {stats.get('errors', 0)}")


def process_alerts():
    """Process job alerts manually."""
    print("📧 Starting job alert processing...")
    
    with app.app_context():
        stats = notification_system.process_all_alerts()
        
        print(f"✅ Job alert processing completed!")
        print(f"📊 Statistics:")
        print(f"   - Alerts processed: {stats.get('alerts_processed', 0)}")
        print(f"   - Emails sent: {stats.get('emails_sent', 0)}")
        print(f"   - Duration: {stats.get('duration', 0):.2f} seconds")
        print(f"   - Errors: {stats.get('errors', 0)}")


def create_test_alert():
    """Create a test job alert."""
    print("🧪 Creating test job alert...")
    
    email = input("Enter email address: ").strip()
    keywords = input("Enter keywords (comma-separated, optional): ").strip()
    location = input("Enter location (optional): ").strip()
    remote_only = input("Remote only? (y/n): ").strip().lower() == 'y'
    salary_min = input("Minimum salary (optional): ").strip()
    
    salary_min_int = None
    if salary_min:
        try:
            salary_min_int = int(salary_min)
        except ValueError:
            print("❌ Invalid salary amount")
            return
    
    with app.app_context():
        alert = notification_system.create_job_alert(
            email=email,
            keywords=keywords if keywords else None,
            location=location if location else None,
            remote_only=remote_only,
            salary_min=salary_min_int
        )
        
        if alert:
            print(f"✅ Job alert created successfully! Alert ID: {alert.id}")
        else:
            print("❌ Failed to create job alert")


def list_alerts():
    """List all active job alerts."""
    print("📋 Listing all active job alerts...")
    
    with app.app_context():
        from custom_libs.models import JobAlert
        alerts = JobAlert.query.filter_by(is_active=True).all()
        
        if not alerts:
            print("📭 No active job alerts found")
            return
        
        print(f"📊 Found {len(alerts)} active alerts:")
        print("-" * 80)
        
        for alert in alerts:
            print(f"ID: {alert.id}")
            print(f"Email: {alert.email}")
            print(f"Keywords: {alert.keywords or 'Any'}")
            print(f"Location: {alert.location or 'Any'}")
            print(f"Remote Only: {'Yes' if alert.remote_only else 'No'}")
            print(f"Min Salary: ${alert.salary_min:,}" if alert.salary_min else "Min Salary: Not specified")
            print(f"Created: {alert.created_at}")
            print(f"Last Notification: {alert.last_notification_sent or 'Never'}")
            print("-" * 80)


def show_rss_stats():
    """Show RSS cache statistics."""
    print("📊 RSS Cache Statistics...")
    
    with app.app_context():
        from custom_libs.models import RSSFeedCache
        
        total_jobs = RSSFeedCache.query.count()
        active_jobs = RSSFeedCache.query.filter_by(is_active=True).count()
        
        # Get jobs by source
        sources = RSSFeedCache.query.with_entities(
            RSSFeedCache.listing_source,
            RSSFeedCache.listing_source
        ).distinct().all()
        
        print(f"📈 Cache Overview:")
        print(f"   - Total jobs: {total_jobs}")
        print(f"   - Active jobs: {active_jobs}")
        print(f"   - Inactive jobs: {total_jobs - active_jobs}")
        
        print(f"\n📋 Jobs by source:")
        for source in sources:
            source_count = RSSFeedCache.query.filter_by(
                listing_source=source[0], 
                is_active=True
            ).count()
            print(f"   - {source[0]}: {source_count} jobs")


def clear_cache():
    """Clear RSS cache."""
    confirm = input("⚠️  Are you sure you want to clear the RSS cache? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ Operation cancelled")
        return
    
    print("🗑️  Clearing RSS cache...")
    
    with app.app_context():
        rss_manager.clear_old_cache()
        print("✅ RSS cache cleared successfully!")


def show_scheduler_status():
    """Show scheduler status and jobs."""
    print("⏰ Scheduler Status...")
    
    with app.app_context():
        jobs = job_scheduler.get_jobs()
        
        if not jobs:
            print("📭 No scheduled jobs found")
            return
        
        print(f"📊 Found {len(jobs)} scheduled jobs:")
        print("-" * 60)
        
        for job in jobs:
            print(f"ID: {job['id']}")
            print(f"Name: {job['name']}")
            print(f"Next Run: {job['next_run_time']}")
            print(f"Trigger: {job['trigger']}")
            print("-" * 60)


def show_help():
    """Show available commands."""
    print("🛠️  Available Commands:")
    print("   update-rss      - Update RSS feeds manually")
    print("   process-alerts  - Process job alerts manually")
    print("   create-alert    - Create a test job alert")
    print("   list-alerts     - List all active job alerts")
    print("   rss-stats       - Show RSS cache statistics")
    print("   clear-cache     - Clear RSS cache")
    print("   scheduler       - Show scheduler status")
    print("   help            - Show this help message")


def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    commands = {
        'update-rss': update_rss,
        'process-alerts': process_alerts,
        'create-alert': create_test_alert,
        'list-alerts': list_alerts,
        'rss-stats': show_rss_stats,
        'clear-cache': clear_cache,
        'scheduler': show_scheduler_status,
        'help': show_help,
    }
    
    if command in commands:
        try:
            commands[command]()
        except KeyboardInterrupt:
            print("\n❌ Operation cancelled by user")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    else:
        print(f"❌ Unknown command: {command}")
        show_help()


if __name__ == "__main__":
    main()
