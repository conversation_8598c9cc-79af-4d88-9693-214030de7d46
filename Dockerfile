# Use multi-stage build to reduce final image size
FROM python:3.10-slim AS builder

WORKDIR /flask-app

# Copy only requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install system dependencies in a single layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    tesseract-ocr \
    poppler-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy application files
COPY app.py .
# COPY .env .  
# Keep this line commented out on the deployed version
COPY ./templates ./templates
COPY ./static ./static
COPY ./cv_storage ./cv_storage
COPY ./custom_libs ./custom_libs

# Use exec form of CMD for production
CMD ["gunicorn", "--workers", "4", "--bind", "0.0.0.0:8080", "app:app"]
