"""
RSS Feed Management System
Handles caching and updating of RSS job feeds for better performance.
"""

import feedparser
import logging
from datetime import datetime
from custom_libs.models import db, RSSFeedCache
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RSSFeedManager:
    """
    Manages RSS feed collection, caching, and updates.
    """
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the RSS manager with Flask app."""
        self.app = app
    
    def collect_and_cache_we_work_remotely(self, keyword=""):
        """
        Collect jobs from WeWorkRemotely RSS feed and cache them in database.
        
        Args:
            keyword (str): Optional keyword filter
            
        Returns:
            int: Number of jobs cached
        """
        try:
            feed_url = "https://weworkremotely.com/remote-jobs.rss"
            logger.info(f"Fetching RSS feed from: {feed_url}")
            
            feed = feedparser.parse(feed_url)
            
            if keyword:
                feed.entries = [
                    item for item in feed.entries
                    if keyword[1:-1].lower() in item.title.split(": ")[1].lower()
                ]
            
            cached_count = 0
            
            for item in feed.entries:
                try:
                    # Parse the RSS item
                    vacancy_data = self._parse_wwr_item(item)
                    if vacancy_data:
                        # Check if already exists
                        existing = RSSFeedCache.query.filter_by(
                            vacancy_id=vacancy_data['vacancy_id']
                        ).first()
                        
                        if existing:
                            # Update existing record
                            for key, value in vacancy_data.items():
                                setattr(existing, key, value)
                            existing.updated_at = datetime.utcnow()
                        else:
                            # Create new record
                            new_vacancy = RSSFeedCache(**vacancy_data)
                            db.session.add(new_vacancy)
                        
                        cached_count += 1
                        
                except Exception as e:
                    logger.error(f"Error processing RSS item: {str(e)}")
                    continue
            
            db.session.commit()
            logger.info(f"Successfully cached {cached_count} jobs from WeWorkRemotely")
            return cached_count
            
        except Exception as e:
            logger.error(f"Error collecting RSS feed: {str(e)}")
            db.session.rollback()
            return 0
    
    def _parse_wwr_item(self, item):
        """
        Parse a WeWorkRemotely RSS item into our vacancy format.
        
        Args:
            item: RSS feed item
            
        Returns:
            dict: Parsed vacancy data or None if parsing fails
        """
        try:
            title_parts = item.title.split(": ")
            if len(title_parts) < 2:
                return None
            
            company = title_parts[0]
            title = title_parts[1]
            
            # Extract description and company URL
            description = item.get("summary", "")
            company_url = ""
            
            try:
                if "<strong>URL:</strong> <a href=" in description:
                    url_part = description.split("<strong>URL:</strong> <a href=")[1]
                    company_url = url_part.split(">")[1].split("<")[0]
                
                # Clean description
                if "</a>\n</p>\n" in description and "<strong>To apply:</strong> <a href=" in description:
                    description = description.split("</a>\n</p>\n")[1].split("<strong>To apply:</strong> <a href=")[0]
            except:
                pass
            
            vacancy_data = {
                'vacancy_id': f"wwr_{hash(item.link)}",
                'vacancy_title': title,
                'employer_name': company,
                'vacancy_job_description': description,
                'vacancy_url': item.link,
                'company_url': company_url,
                'vacancy_creation_date': datetime.now(),
                'vacancy_country': item.get("region", "Remote"),
                'vacancy_city': "",
                'employer_logo_url': item.get("media_content", [{}])[0].get("url", ""),
                'salary_min': "Unspecified",
                'salary_max': "",
                'salary_currency': "Unknown",
                'vacancy_type': item.get("type", "Full-time"),
                'office_schedule': "Remote",
                'listing_source': "WeWorkRemotely",
                'is_active': True
            }
            
            # Skip if no logo URL (as per original logic)
            if not vacancy_data['employer_logo_url']:
                return None
                
            return vacancy_data
            
        except Exception as e:
            logger.error(f"Error parsing RSS item: {str(e)}")
            return None
    
    def get_cached_vacancies(self, keyword="", limit=200):
        """
        Get cached RSS vacancies from database.
        
        Args:
            keyword (str): Optional keyword filter
            limit (int): Maximum number of results
            
        Returns:
            list: List of cached vacancy dictionaries
        """
        try:
            query = RSSFeedCache.query.filter_by(is_active=True)
            
            if keyword:
                keyword_filter = f"%{keyword.strip('%')}%"
                query = query.filter(
                    RSSFeedCache.vacancy_title.ilike(keyword_filter) |
                    RSSFeedCache.employer_name.ilike(keyword_filter)
                )
            
            vacancies = query.order_by(RSSFeedCache.created_at.desc()).limit(limit).all()
            
            # Convert to dictionary format for compatibility
            result = []
            for vacancy in vacancies:
                vacancy_dict = {
                    'vacancy_id': vacancy.vacancy_id,
                    'vacancy_title': vacancy.vacancy_title,
                    'employer_name': vacancy.employer_name,
                    'vacancy_job_description': vacancy.vacancy_job_description,
                    'vacancy_url': vacancy.vacancy_url,
                    'company_url': vacancy.company_url,
                    'vacancy_creation_date': vacancy.vacancy_creation_date,
                    'vacancy_country': vacancy.vacancy_country,
                    'vacancy_city': vacancy.vacancy_city,
                    'jobtags': [],
                    'employer_logo_url': vacancy.employer_logo_url,
                    'vacancy_status': 'Active',
                    'salary_min': vacancy.salary_min,
                    'salary_max': vacancy.salary_max,
                    'salary_currency': vacancy.salary_currency,
                    'vacancy_type': vacancy.vacancy_type,
                    'office_schedule': vacancy.office_schedule,
                    'listing_source': vacancy.listing_source,
                    'employer_banner_url': ""
                }
                result.append(vacancy_dict)
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting cached vacancies: {str(e)}")
            return []
    
    def clear_old_cache(self):
        """
        Clear old cached RSS entries to make room for fresh data.
        """
        try:
            # Mark all RSS entries as inactive instead of deleting
            RSSFeedCache.query.update({'is_active': False})
            db.session.commit()
            logger.info("Cleared old RSS cache entries")
            
        except Exception as e:
            logger.error(f"Error clearing old cache: {str(e)}")
            db.session.rollback()
    
    def update_all_feeds(self):
        """
        Update all RSS feeds. Called by the daily scheduler.
        
        Returns:
            dict: Update statistics
        """
        logger.info("Starting daily RSS feed update")
        
        stats = {
            'total_cached': 0,
            'errors': 0,
            'start_time': datetime.utcnow()
        }
        
        try:
            # Clear old cache
            self.clear_old_cache()
            
            # Update WeWorkRemotely feed
            wwr_count = self.collect_and_cache_we_work_remotely()
            stats['total_cached'] += wwr_count
            
            # Add more RSS feeds here in the future
            # stats['total_cached'] += self.collect_and_cache_other_feed()
            
            stats['end_time'] = datetime.utcnow()
            stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
            
            logger.info(f"RSS feed update completed. Cached {stats['total_cached']} jobs in {stats['duration']:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Error during RSS feed update: {str(e)}")
            stats['errors'] += 1
        
        return stats


# Global RSS manager instance
rss_manager = RSSFeedManager()
