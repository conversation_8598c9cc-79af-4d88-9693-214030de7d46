"""
Database models for the job portal application.
This file contains SQLAlchemy models to replace raw SQL queries with more pythonic ORM operations.
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship

db = SQLAlchemy()


class RSSFeedCache(db.Model):
    """
    Table to cache RSS feed job listings for faster loading.
    Updated daily to replace live RSS fetching.
    """
    __tablename__ = 'rss_feed_cache'
    
    id = Column(Integer, primary_key=True)
    vacancy_id = Column(String(255), unique=True, nullable=False)
    vacancy_title = Column(String(500), nullable=False)
    employer_name = Column(String(255), nullable=False)
    vacancy_job_description = Column(Text)
    vacancy_url = Column(String(1000))
    company_url = Column(String(1000))
    vacancy_creation_date = Column(DateTime)
    vacancy_country = Column(String(100))
    vacancy_city = Column(String(100))
    employer_logo_url = Column(String(1000))
    salary_min = Column(String(50))
    salary_max = Column(String(50))
    salary_currency = Column(String(10))
    vacancy_type = Column(String(100))
    office_schedule = Column(String(100))
    listing_source = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class JobAlert(db.Model):
    """
    User job alerts for email notifications when matching jobs are found.
    """
    __tablename__ = 'job_alerts'
    
    id = Column(Integer, primary_key=True)
    email = Column(String(255), nullable=False)
    keywords = Column(String(500))  # Comma-separated keywords
    location = Column(String(255))
    remote_only = Column(Boolean, default=False)
    salary_min = Column(Integer)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_notification_sent = Column(DateTime)
    
    # Relationship to notifications
    notifications = relationship("JobNotification", back_populates="alert")


class JobNotification(db.Model):
    """
    Track sent notifications to avoid duplicate emails.
    """
    __tablename__ = 'job_notifications'
    
    id = Column(Integer, primary_key=True)
    alert_id = Column(Integer, ForeignKey('job_alerts.id'), nullable=False)
    vacancy_id = Column(String(255), nullable=False)
    sent_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship to job alert
    alert = relationship("JobAlert", back_populates="notifications")


class Employer(db.Model):
    """
    Employer model to replace direct SQL queries.
    """
    __tablename__ = 'feed_employer'
    
    employer_id = Column(Integer, primary_key=True)
    employer_name = Column(String(255), nullable=False)
    employer_email = Column(String(255))
    employer_logo_url = Column(String(1000))
    employer_banner_url = Column(String(1000))
    employer_description = Column(Text)
    employer_industry = Column(String(255))
    employer_social_portals = Column(Text)
    office_locations = Column(Text)
    employer_created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    vacancies = relationship("Vacancy", back_populates="employer")


class Vacancy(db.Model):
    """
    Vacancy model to replace direct SQL queries.
    """
    __tablename__ = 'feed_vacancy'
    
    vacancy_id = Column(Integer, primary_key=True)
    employer_id = Column(Integer, ForeignKey('feed_employer.employer_id'), nullable=False)
    vacancy_title = Column(String(500), nullable=False)
    vacancy_job_description = Column(Text)
    vacancy_creation_date = Column(DateTime, default=datetime.utcnow)
    vacancy_status = Column(String(50), default='Active')
    vacancy_country = Column(String(100))
    vacancy_city = Column(String(100))
    salary_min = Column(Integer)
    salary_max = Column(Integer)
    salary_currency = Column(String(10))
    vacancy_type = Column(String(100))
    office_schedule = Column(String(100))
    vacancy_experience = Column(String(255))
    vacancy_education = Column(String(255))
    jobtags = Column(JSON)
    job_portals = Column(String(500))
    
    # Relationships
    employer = relationship("Employer", back_populates="vacancies")
    applications = relationship("Application", back_populates="vacancy")


class Candidate(db.Model):
    """
    Candidate model to replace direct SQL queries.
    """
    __tablename__ = 'feed_candidate'
    
    candidate_id = Column(Integer, primary_key=True)
    candidate_firstname = Column(String(100), nullable=False)
    candidate_lastname = Column(String(100), nullable=False)
    candidate_email = Column(String(255), nullable=False)
    candidate_phone = Column(String(50))
    candidate_address = Column(String(500))
    candidate_date_of_birth = Column(DateTime)
    avatar_bg_color = Column(String(10))
    candidate_created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    applications = relationship("Application", back_populates="candidate")


class Application(db.Model):
    """
    Application model to replace direct SQL queries.
    """
    __tablename__ = 'feed_application'
    
    application_id = Column(Integer, primary_key=True)
    candidate_id = Column(Integer, ForeignKey('feed_candidate.candidate_id'), nullable=False)
    vacancy_id = Column(Integer, ForeignKey('feed_vacancy.vacancy_id'), nullable=False)
    application_date = Column(DateTime, default=datetime.utcnow)
    application_source = Column(String(100))
    application_status = Column(String(50), default='Active')
    application_state = Column(String(50), default='New')
    current_employer = Column(String(255))
    current_position = Column(String(255))
    education_level = Column(String(100))
    notice_period = Column(String(100))
    total_exp_years = Column(Integer, default=0)
    score = Column(Integer, default=-1)
    cv_location = Column(String(1000))
    
    # Relationships
    candidate = relationship("Candidate", back_populates="applications")
    vacancy = relationship("Vacancy", back_populates="applications")


class TalentPool(db.Model):
    """
    Talent pool model to replace direct SQL queries.
    """
    __tablename__ = 'feed_talentpool'
    
    talent_id = Column(Integer, primary_key=True)
    talent_firstname = Column(String(100), nullable=False)
    talent_lastname = Column(String(100), nullable=False)
    talent_phone = Column(String(50))
    talent_email = Column(String(255), nullable=False)
    talent_country = Column(String(100))
    cv_location = Column(String(1000))
    talent_status = Column(String(50), default='Active')
    talent_added_at = Column(DateTime, default=datetime.utcnow)
